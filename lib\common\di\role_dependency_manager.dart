import 'package:get/get.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/role_repository.dart';
import 'package:rolio/modules/role/repository/recommend_repository.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/modules/role/service/search_service.dart';

/// 角色模块统一依赖管理器
/// 
/// 负责管理角色模块的所有依赖注入，避免重复注册和循环依赖
/// 使用单例模式确保依赖的一致性
class RoleDependencyManager {
  static RoleDependencyManager? _instance;
  static RoleDependencyManager get instance => _instance ??= RoleDependencyManager._();
  
  RoleDependencyManager._();
  
  // 依赖注册状态跟踪
  bool _repositoriesRegistered = false;
  bool _servicesRegistered = false;
  
  /// 注册所有角色模块的依赖
  /// 
  /// [forceRefresh] 是否强制重新注册所有依赖
  void registerAllDependencies({bool forceRefresh = false}) {
    try {
      LogUtil.info('开始注册角色模块依赖...');
      
      if (forceRefresh) {
        _clearAllDependencies();
      }
      
      _registerRepositories();
      _registerServices();
      
      LogUtil.info('角色模块依赖注册完成');
    } catch (e) {
      LogUtil.error('角色模块依赖注册失败: $e');
      rethrow;
    }
  }
  
  /// 注册仓库层依赖
  void _registerRepositories() {
    if (_repositoriesRegistered && !Get.isRegistered<RoleRepository>()) {
      _repositoriesRegistered = false; // 重置状态
    }
    
    if (_repositoriesRegistered) {
      LogUtil.debug('仓库依赖已注册，跳过');
      return;
    }
    
    try {
      // 注册角色仓库
      if (!Get.isRegistered<RoleRepository>()) {
        Get.put<RoleRepository>(RoleRepository(), permanent: true);
        LogUtil.debug('注册仓库: RoleRepository');
      }
      
      // 注册推荐仓库
      if (!Get.isRegistered<RecommendRepository>()) {
        Get.put<RecommendRepository>(RecommendRepository(), permanent: true);
        LogUtil.debug('注册仓库: RecommendRepository');
      }
      
      // 注册搜索仓库
      if (!Get.isRegistered<SearchRepository>()) {
        Get.put<SearchRepository>(SearchRepository(), permanent: true);
        LogUtil.debug('注册仓库: SearchRepository');
      }
      
      _repositoriesRegistered = true;
      LogUtil.debug('所有仓库依赖注册完成');
    } catch (e) {
      LogUtil.error('注册仓库依赖失败: $e');
      throw e;
    }
  }
  
  /// 注册服务层依赖
  void _registerServices() {
    if (_servicesRegistered && !Get.isRegistered<RecommendService>()) {
      _servicesRegistered = false; // 重置状态
    }
    
    if (_servicesRegistered) {
      LogUtil.debug('服务依赖已注册，跳过');
      return;
    }
    
    try {
      // 确保仓库已注册
      _registerRepositories();
      
      // 注册推荐服务（同时实现IRoleProvider接口）
      if (!Get.isRegistered<RecommendService>()) {
        final recommendService = RecommendService();
        Get.put<RecommendService>(recommendService, permanent: true);
        
        // 注册接口实现
        if (!Get.isRegistered<IRoleProvider>()) {
          Get.put<IRoleProvider>(recommendService, permanent: true);
        }
        
        LogUtil.debug('注册服务: RecommendService 和接口: IRoleProvider');
      }
      
      // 注册角色服务
      if (!Get.isRegistered<RoleService>()) {
        Get.put<RoleService>(RoleService(), permanent: true);
        LogUtil.debug('注册服务: RoleService');
      }
      
      // 注册搜索服务
      if (!Get.isRegistered<SearchService>()) {
        Get.put<SearchService>(SearchService(), permanent: true);
        LogUtil.debug('注册服务: SearchService');
      }
      
      _servicesRegistered = true;
      LogUtil.debug('所有服务依赖注册完成');
    } catch (e) {
      LogUtil.error('注册服务依赖失败: $e');
      throw e;
    }
  }
  
  /// 检查依赖是否已正确注册
  bool checkDependencies() {
    final requiredDependencies = [
      RoleRepository,
      RecommendRepository,
      SearchRepository,
      RecommendService,
      RoleService,
      SearchService,
      IRoleProvider,
    ];
    
    for (final dependency in requiredDependencies) {
      if (!Get.isRegistered(dependency)) {
        LogUtil.warn('依赖未注册: $dependency');
        return false;
      }
    }
    
    LogUtil.debug('所有必需依赖已正确注册');
    return true;
  }
  
  /// 清除所有依赖（用于测试或重置）
  void _clearAllDependencies() {
    try {
      LogUtil.debug('清除角色模块所有依赖...');
      
      // 清除服务
      if (Get.isRegistered<SearchService>()) Get.delete<SearchService>(force: true);
      if (Get.isRegistered<RoleService>()) Get.delete<RoleService>(force: true);
      if (Get.isRegistered<RecommendService>()) Get.delete<RecommendService>(force: true);
      if (Get.isRegistered<IRoleProvider>()) Get.delete<IRoleProvider>(force: true);
      
      // 清除仓库
      if (Get.isRegistered<SearchRepository>()) Get.delete<SearchRepository>(force: true);
      if (Get.isRegistered<RecommendRepository>()) Get.delete<RecommendRepository>(force: true);
      if (Get.isRegistered<RoleRepository>()) Get.delete<RoleRepository>(force: true);
      
      _repositoriesRegistered = false;
      _servicesRegistered = false;
      
      LogUtil.debug('角色模块依赖清除完成');
    } catch (e) {
      LogUtil.error('清除依赖失败: $e');
    }
  }
  
  /// 获取依赖注册状态信息
  Map<String, dynamic> getDependencyStatus() {
    return {
      'repositoriesRegistered': _repositoriesRegistered,
      'servicesRegistered': _servicesRegistered,
      'allDependenciesValid': checkDependencies(),
      'registeredDependencies': {
        'RoleRepository': Get.isRegistered<RoleRepository>(),
        'RecommendRepository': Get.isRegistered<RecommendRepository>(),
        'SearchRepository': Get.isRegistered<SearchRepository>(),
        'RecommendService': Get.isRegistered<RecommendService>(),
        'RoleService': Get.isRegistered<RoleService>(),
        'SearchService': Get.isRegistered<SearchService>(),
        'IRoleProvider': Get.isRegistered<IRoleProvider>(),
      }
    };
  }
}
