import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/di/role_dependency_manager.dart';
import 'package:rolio/modules/role/controller/search_controller.dart';

/// 搜索模块绑定类（重构版本）
///
/// 使用统一的依赖管理器，简化依赖注入逻辑
/// 负责搜索模块相关的控制器绑定
class SearchBinding implements Bindings {
  @override
  void dependencies() {
    try {
      LogUtil.info('开始注册SearchBinding依赖...');

      // 使用统一的依赖管理器确保所有依赖已注册
      RoleDependencyManager.instance.registerAllDependencies();

      // 验证依赖是否正确注册
      if (!RoleDependencyManager.instance.checkDependencies()) {
        throw Exception('角色模块依赖注册不完整');
      }

      // 注册控制器
      _registerControllers();

      LogUtil.info('SearchBinding依赖注册完成');
    } catch (e) {
      LogUtil.error('SearchBinding依赖注册失败: $e');
      rethrow;
    }
  }

  /// 注册控制器
  void _registerControllers() {
    try {
      // 清理已存在的控制器
      if (Get.isRegistered<SearchController>()) {
        LogUtil.debug('移除已存在的SearchController');
        Get.delete<SearchController>(force: true);
      }

      // 注册搜索控制器
      Get.lazyPut<SearchController>(() => SearchController(), fenix: true);
      LogUtil.debug('注册控制器: SearchController');

    } catch (e) {
      LogUtil.error('注册搜索控制器失败: $e');
      rethrow;
    }
  }
}