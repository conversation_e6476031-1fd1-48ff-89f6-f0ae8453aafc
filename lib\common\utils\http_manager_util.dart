import "dart:convert";
import "dart:async";

import "package:dio/dio.dart" as dio;
import "package:rolio/common/utils/logger.dart";
import "package:rolio/common/utils/error_handler.dart";
import "package:rolio/common/constants/error_codes.dart";
import "package:get/get.dart";
import "package:rolio/manager/global_state.dart";
import "package:rolio/common/event/event_bus.dart";

const int apiEncryptionIndex = 15; // 设置加密索引
const String unicodePrefix = "\\u"; // Unicode前缀常量

/// API异常类 - 专门用于API响应错误
class ApiException implements Exception {
  final int code;
  final String message;
  final String errorCode;

  ApiException(this.code, this.message, {String? errorCode})
      : this.errorCode = errorCode ?? ErrorCodes.SERVER_ERROR;

  @override
  String toString() => 'ApiException(code: $code, message: $message, errorCode: $errorCode)';
}

/// 网络异常类
class NetworkException extends AppException {
  final int? statusCode;
  
  NetworkException(String message, {String? code, dynamic originalError, this.statusCode})
      : super(message, code: code ?? ErrorCodes.NETWORK_ERROR, originalError: originalError);
}

/// 服务器异常类
class ServerException extends AppException {
  ServerException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? ErrorCodes.SERVER_ERROR, originalError: originalError);
}

/// 认证异常类
class AuthException extends AppException {
  AuthException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? ErrorCodes.AUTH_ERROR, originalError: originalError);
}

/// 数据异常类
class DataException extends AppException {
  DataException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? ErrorCodes.DATA_PARSE_ERROR, originalError: originalError);
}

/// 请求重复异常类
class DuplicateRequestException extends AppException {
  DuplicateRequestException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? ErrorCodes.DUPLICATE_REQUEST, originalError: originalError);
}

class HttpManager {
  static final dio.Dio _dio = _createDio();
  // 用于管理取消令牌的映射
  static final Map<String, dio.CancelToken> _cancelTokens = {};
  // 用于管理活跃请求的映射
  static final Map<String, Future<dynamic>> _activeRequests = {};
  // 是否启用请求去重
  static bool _enableDuplicateRequestCheck = true;
  
  // 认证流程状态标志
  static bool _isAuthProcessing = false;
  
  // 静态构造函数，用于初始化
  static void init() {
    _setupAuthProcessListeners();
  }
  
  // 设置认证流程事件监听器
  static void _setupAuthProcessListeners() {
    EventBus().on('auth_process_started').listen((event) {
      LogUtil.info('HTTP管理器 - 收到认证开始事件，暂停HTTP请求');
      _isAuthProcessing = true;
    });
    
    EventBus().on('auth_process_completed').listen((event) {
      LogUtil.info('HTTP管理器 - 收到认证完成事件，恢复HTTP请求');
      _isAuthProcessing = false;
    });
  }
  
  /// 获取GlobalState中的token
  static Future<String?> _getLatestTokenAsync() async {
    try {
      // 从GlobalState获取token
      if (Get.isRegistered<GlobalState>()) {
        final globalState = Get.find<GlobalState>();
        final stateToken = globalState.accessToken.value;
        if (stateToken.isNotEmpty) {
          return stateToken;
        }
      }
      return null;
    } catch (e) {
      LogUtil.error('获取GlobalState中的token失败: $e');
      return null;
    }
  }
  
  // 设置用户token的方法 - 仅作为GlobalState的辅助方法存在
  static Future<void> setTokenAsync(String token) async {
    // 此方法仅被GlobalState调用，内部不再存储token
    LogUtil.debug('HttpManager同步token完成');
  }
  
  // 清除用户token的方法 - 仅作为GlobalState的辅助方法存在
  static Future<void> clearTokenAsync() async {
    // 此方法仅被GlobalState调用，内部不再存储token
    LogUtil.debug('HttpManager清除token完成');
  }
  
  // 获取当前token（异步版本）
  static Future<String?> getTokenAsync() => _getLatestTokenAsync();
  
  // 创建Dio实例并配置
  static dio.Dio _createDio() {
    final dioInstance = dio.Dio(dio.BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 15),
      contentType: 'application/json',
    ));

    // 添加拦截器
    dioInstance.interceptors.add(dio.InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 自动添加token到请求头 - 每次请求都从GlobalState获取最新token
        final latestToken = await _getLatestTokenAsync();
        if (latestToken != null && latestToken.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $latestToken';
        }
        
        LogUtil.debug("=== HTTP请求开始 ===");
        LogUtil.debug("请求URL: ${options.uri}");
        LogUtil.debug("请求方法: ${options.method}");
        LogUtil.debug("请求头: ${options.headers}");
        LogUtil.debug("请求数据: ${options.data}");
        return handler.next(options);
      },
      onResponse: (response, handler) {
        LogUtil.debug("=== HTTP响应开始 ===");
        LogUtil.debug("响应状态码: ${response.statusCode}");
        LogUtil.debug("响应数据: ${response.data}");
        LogUtil.debug("=== HTTP响应结束 ===");
        return handler.next(response);
      },
      onError: (dio.DioException e, handler) {
        LogUtil.error('=== HTTP请求错误 ===');
        LogUtil.error('错误类型: ${e.type}');
        LogUtil.error('错误消息: ${e.message}');
        if (e.response != null) {
          LogUtil.error('响应状态码: ${e.response?.statusCode}');
          LogUtil.error('响应数据: ${e.response?.data}');
          
          // 处理401错误（未授权），可能是token过期
          if (e.response?.statusCode == 401) {
            // 清理Token并发送Token过期事件
            _handleTokenExpiration();
          }
        }
        LogUtil.error('=== HTTP错误详情结束 ===');
        return handler.next(e);
      },
    ));

    return dioInstance;
  }

  /// 设置是否启用请求去重
  static void setEnableDuplicateRequestCheck(bool enable) {
    _enableDuplicateRequestCheck = enable;
    LogUtil.debug('请求去重检查已${enable ? "启用" : "禁用"}');
  }

  /// 生成请求的唯一标识符
  static String _generateRequestKey(String method, String url, Map<String, dynamic>? params, dynamic body) {
    final StringBuffer buffer = StringBuffer();
    buffer.write('$method:$url');
    
    // 添加查询参数
    if (params != null && params.isNotEmpty) {
      final sortedParams = Map.fromEntries(params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));
      buffer.write('?');
      sortedParams.forEach((key, value) {
        buffer.write('$key=$value&');
      });
    }
    
    // 添加请求体
    if (body != null) {
      if (body is String) {
        buffer.write(body);
      } else {
        try {
          buffer.write(jsonEncode(body));
        } catch (e) {
          // 如果无法序列化，使用toString()
          buffer.write(body.toString());
        }
      }
    }
    
    return buffer.toString();
  }

  /// 检查是否有相同的请求正在进行中
  static Future<T> _checkDuplicateRequest<T>(
    String method, 
    String url, 
    Map<String, dynamic>? params, 
    dynamic body,
    Future<T> Function() requestFunc,
    {bool allowDuplicate = false}
  ) async {
    // 检查是否在认证过程中
    if (_isAuthProcessing) {
      // 如果请求是登录/登出相关请求，允许通过
      if (!_isAuthRelatedRequest(url)) {
        LogUtil.warn('请求被暂停 - 认证流程进行中: $method $url');
        throw NetworkException(
          '请求暂停 - 认证流程进行中', 
          code: ErrorCodes.AUTH_IN_PROGRESS,
        );
      } else {
        LogUtil.debug('允许认证相关请求通过: $method $url');
      }
    }
    
    if (!_enableDuplicateRequestCheck || allowDuplicate) {
      return await requestFunc();
    }
    
    final requestKey = _generateRequestKey(method, url, params, body);
    
    // 检查是否有相同的请求正在进行中
    if (_activeRequests.containsKey(requestKey)) {
      LogUtil.warn('检测到重复请求: $method $url');
      try {
        // 等待现有请求完成并返回其结果
        return await _activeRequests[requestKey] as T;
      } catch (e) {
        // 如果现有请求失败，允许新请求继续
        LogUtil.debug('现有请求失败，允许新请求继续: $e');
      }
    }
    
    // 创建新请求并存储
    final completer = Completer<T>();
    _activeRequests[requestKey] = completer.future;
    
    try {
      final result = await requestFunc();
      completer.complete(result);
      return result;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      // 请求完成后从活跃请求中移除
      _activeRequests.remove(requestKey);
    }
  }
  
  /// 判断是否为认证相关请求
  static bool _isAuthRelatedRequest(String url) {
    // 检查URL是否包含认证相关路径
    final authRelatedPaths = [
      '/auth', 
      '/login', 
      '/logout', 
      '/register', 
      '/password',
      '/signin',
      '/signout',
      '/token'
    ];
    
    for (final path in authRelatedPaths) {
      if (url.toLowerCase().contains(path.toLowerCase())) {
        return true;
      }
    }
    
    return false;
  }

  // 获取或创建CancelToken
  static dio.CancelToken _getCancelToken(String? tokenId) {
    if (tokenId == null) {
      return dio.CancelToken();
    }
    
    if (!_cancelTokens.containsKey(tokenId)) {
      _cancelTokens[tokenId] = dio.CancelToken();
    }
    
    return _cancelTokens[tokenId]!;
  }

  // 获取全局状态中的token
  static Future<String?> _getGlobalStateToken() async {
    try {
      if (Get.isRegistered<GlobalState>()) {
        final globalState = Get.find<GlobalState>();
        final token = globalState.accessToken.value;
        if (token.isNotEmpty) {
          return token;
        }
      }
    } catch (e) {
      LogUtil.error('获取GlobalState中的token失败: $e');
    }
    return null;
  }

  // GET请求
  static Future<Response<T>> get<T>({
    required String url,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    Map<String, dynamic>? params,
    bool? isEncrypt,
    String? cancelToken,
    bool allowDuplicate = false,
  }) async {
    return _checkDuplicateRequest<Response<T>>(
      'GET',
      url,
      params,
      null,
      () async {
        try {
          final dioToken = cancelToken != null ? _getCancelToken(cancelToken) : null;
          
          final response = await _dio.get(
            url,
            queryParameters: params,
            options: dio.Options(headers: headers),
            cancelToken: dioToken,
          );
          return _processResponse(response, fromJsonT, isEncrypt);
        } on dio.DioException catch (e) {
          throw _handleDioException(e, url);
        } catch (e) {
          LogUtil.error('HttpManager GET request error,url:$url,detail: $e');
          rethrow;
        }
      },
      allowDuplicate: allowDuplicate,
    );
  }

  // POST请求
  static Future<Response<T>> post<T>({
    required String url,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    dynamic body,
    bool? isEncrypt,
    String? cancelToken,
    bool allowDuplicate = false,
  }) async {
    return _checkDuplicateRequest<Response<T>>(
      'POST',
      url,
      null,
      body,
      () async {
        try {
          var reqBody = jsonEncode(body);
          if (isEncrypt == true) {
            List<int> reqBodyBytes = utf8.encode(reqBody);
            reqBodyBytes = _encryptionData(reqBodyBytes);
            reqBody = String.fromCharCodes(reqBodyBytes);
          }

          final dioToken = cancelToken != null ? _getCancelToken(cancelToken) : null;

          final response = await _dio.post(
            url,
            data: reqBody,
            options: dio.Options(headers: headers),
            cancelToken: dioToken,
          );

          return _processResponse(response, fromJsonT, isEncrypt);
        } on dio.DioException catch (e) {
          throw _handleDioException(e, url);
        } catch (e) {
          LogUtil.error('HttpManager POST request error,url:$url,detail: $e');
          rethrow;
        }
      },
      allowDuplicate: allowDuplicate,
    );
  }

  // 处理Dio异常
  static Exception _handleDioException(dio.DioException e, String url) {
    switch (e.type) {
      case dio.DioExceptionType.connectionTimeout:
      case dio.DioExceptionType.sendTimeout:
      case dio.DioExceptionType.receiveTimeout:
        return NetworkException('Connection timeout, please check your network', code: ErrorCodes.NETWORK_ERROR);
      case dio.DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        if (statusCode != null && statusCode >= 500) {
          return ServerException('Server internal error', code: ErrorCodes.SERVER_ERROR);
        } else if (statusCode == 401) {
          return AuthException('Authentication failed, please login again', code: ErrorCodes.AUTH_ERROR);
        } else if (statusCode == 403) {
          return AuthException('Permission denied', code: ErrorCodes.AUTH_ERROR);
        } else if (statusCode == 404) {
          return ServerException('The requested resource does not exist', code: ErrorCodes.DATA_NOT_FOUND);
        } else {
          return NetworkException('Server response error', code: ErrorCodes.NETWORK_ERROR);
        }
      case dio.DioExceptionType.cancel:
        return NetworkException('Request cancelled', code: ErrorCodes.NETWORK_ERROR);
      case dio.DioExceptionType.connectionError:
        return NetworkException('Network connection error, please check your network', code: ErrorCodes.NETWORK_ERROR);
      case dio.DioExceptionType.unknown:
      default:
        return NetworkException('Unknown network error: ${e.message}', code: ErrorCodes.GENERAL_ERROR);
    }
  }

  // 处理响应
  static Response<T> _processResponse<T>(
    dio.Response response,
    T Function(dynamic)? fromJsonT,
    bool? isDecrypt,
  ) {
    if (response.statusCode! >= 200 && response.statusCode! < 300) {
      dynamic respData = response.data;
      
      // 检查响应数据是否为null
      if (respData == null) {
        LogUtil.warn('HTTP响应数据为null');
        return Response<T>(
          code: 0,
          msg: 'Server returned empty data',
          data: null,
          rawData: null,
        );
      }
      
      String jsonString;

      try {
        if (respData is String) {
          List<int> respBodyBytes = utf8.encode(respData);
          if (isDecrypt == true) {
            respBodyBytes = _decryptionData(respBodyBytes);
          }
          jsonString = utf8.decode(respBodyBytes);
        } else {
          // 如果响应已经是JSON对象，直接使用
          //LogYtilinfo("响应已经是JSON对象: $respData");
          
          // 对于AI接口，检查返回的JSON结构
          if (respData is Map<String, dynamic>) {
            // 检查是否包含AI接口特定的结构
            if (respData.containsKey('code') && respData.containsKey('message') && respData.containsKey('data')) {
              final code = respData['code'] as int;
              final message = respData['message'] as String;
              final data = respData['data'];
              
              // 创建API响应
              if (fromJsonT != null && data != null) {
                try {
                  final parsedData = fromJsonT(data);
                  return Response<T>(
                    code: code,
                    msg: message,
                    data: parsedData,
                    rawData: respData,
                  );
                } catch (e) {
                  return Response<T>(
                    code: 0,
                    msg: 'JSON parsing error: $e',
                    data: null,
                    rawData: respData,
                  );
                }
              } else {
                return Response<T>(
                  code: code,
                  msg: message,
                  data: null,
                  rawData: respData,
                );
              }
            }
          }
          
          // 兼容接口返回data为null的情况
          if (fromJsonT != null) {
            try {
              final data = fromJsonT(respData);
              return Response<T>(
                code: 200,
                msg: 'Success',
                data: data,
                rawData: respData,
              );
            } catch (e) {
              //LogYtilerror("解析JSON数据失败: $e");
              return Response<T>(
                code: 0,
                msg: 'JSON parsing error: $e',
                data: null,
                rawData: respData,
              );
            }
          } else {
            return Response<T>(
              code: 200,
              msg: 'Success',
              data: null,
              rawData: respData,
            );
          }
        }

        if (jsonString.isEmpty) {
          return Response<T>(
            code: 0,
            msg: 'Empty response',
            data: null,
            rawData: respData,
          );
        }

        final jsonResponse = jsonDecode(jsonString);
        if (jsonResponse is Map<String, dynamic>) {
          final res = Response<T>.fromJson(jsonResponse, fromJsonT);

          if (!res.isSuccess) {
            throw ApiException(res.code, res.msg, errorCode: res.errorCode);
          }
          return res;
        } else {
          //LogYtilerror("响应数据格式不是预期的Map<String, dynamic>: $jsonResponse");
          return Response<T>(
            code: 0,
            msg: 'Response data format error',
            data: null,
            rawData: respData,
            errorCode: ErrorCodes.SERVER_ERROR,
          );
        }
      } catch (e) {
        //LogYtilerror("处理响应数据失败: $e");
        return Response<T>(
          code: 0,
          msg: 'Failed to process response: $e',
          data: null,
          rawData: respData,
          errorCode: ErrorCodes.SERVER_ERROR,
        );
      }
    } else {
      throw NetworkException(
        'HTTP request failed: ${response.statusCode}',
        code: 'HTTP_ERROR_${response.statusCode}',
        statusCode: response.statusCode,
      );
    }
  }

  // 解密数据
  static List<int> _decryptionData(List<int> data) {
    List<int> result = [];
    for (int i = 0; i < data.length; i++) {
      int ascii = data[i] - apiEncryptionIndex;
      if (ascii < 0) {
        ascii += 128;
      }
      result.add(ascii);
    }
    return result;
  }

  // 加密数据
  static List<int> _encryptionData(List<int> data) {
    List<int> paramsData = [];
    final utf8Decoder = const Utf8Decoder();
    final String decodedString = utf8Decoder.convert(data);
    
    for (int c in decodedString.runes) {
      if (c >= 128) {
        // 多字节字符转换为unicode
        String d = "$unicodePrefix${c.toRadixString(16)}";
        for (int c2 in d.runes) {
          int ascii = c2 + apiEncryptionIndex;
          if (ascii >= 128) {
            ascii -= 128;
          }
          paramsData.add(ascii);
        }
      } else {
        int ascii = c + apiEncryptionIndex;
        if (ascii >= 128) {
          ascii -= 128;
        }
        paramsData.add(ascii);
      }
    }
    return paramsData;
  }

  // DELETE请求
  static Future<Response<T>> delete<T>({
    required String url,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    dynamic body,
    bool? isEncrypt,
    String? cancelToken,
    bool allowDuplicate = false,
  }) async {
    return _checkDuplicateRequest<Response<T>>(
      'DELETE',
      url,
      null,
      body,
      () async {
        try {
          final dioToken = cancelToken != null ? _getCancelToken(cancelToken) : null;
          
          final response = await _dio.delete(
            url,
            data: body != null ? jsonEncode(body) : null,
            options: dio.Options(headers: headers),
            cancelToken: dioToken,
          );

          return _processResponse(response, fromJsonT, isEncrypt);
        } on dio.DioException catch (e) {
          throw _handleDioException(e, url);
        } catch (e) {
          LogUtil.error('HttpManager DELETE request error,url:$url,detail: $e');
          rethrow;
        }
      },
      allowDuplicate: allowDuplicate,
    );
  }
  
  // PUT请求
  static Future<Response<T>> put<T>({
    required String url,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    dynamic body,
    bool? isEncrypt,
    String? cancelToken,
    bool allowDuplicate = false,
  }) async {
    return _checkDuplicateRequest<Response<T>>(
      'PUT',
      url,
      null,
      body,
      () async {
        try {
          final dioToken = cancelToken != null ? _getCancelToken(cancelToken) : null;
          
          final response = await _dio.put(
            url,
            data: body != null ? jsonEncode(body) : null,
            options: dio.Options(headers: headers),
            cancelToken: dioToken,
          );

          return _processResponse(response, fromJsonT, isEncrypt);
        } on dio.DioException catch (e) {
          throw _handleDioException(e, url);
        } catch (e) {
          LogUtil.error('HttpManager PUT request error,url:$url,detail: $e');
          rethrow;
        }
      },
      allowDuplicate: allowDuplicate,
    );
  }

  // 取消请求
  static void cancelRequests(String cancelToken) {
    if (_cancelTokens.containsKey(cancelToken)) {
      _cancelTokens[cancelToken]!.cancel('Request cancelled by user');
      _cancelTokens.remove(cancelToken);
      //LogYtilinfo('已取消请求: $cancelToken');
    }
  }

  // 清除所有取消令牌
  static void clearAllCancelTokens() {
    for (var token in _cancelTokens.values) {
      token.cancel('All requests cancelled');
    }
    _cancelTokens.clear();
    //LogYtilinfo('已清除所有取消令牌');
  }

  // 清除所有活跃请求
  static void clearAllActiveRequests() {
    _activeRequests.clear();
    LogUtil.debug('已清除所有活跃请求');
  }

  // 文件上传
  static Future<Response<T>> uploadFile<T>({
    required String url,
    required String filePath,
    required String fileName,
    Map<String, dynamic>? extraData,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    Function(int sent, int total)? onSendProgress,
    String? cancelToken,
    bool allowDuplicate = true, // 默认允许文件上传重复，因为文件上传通常是用户主动操作
  }) async {
    return _checkDuplicateRequest<Response<T>>(
      'UPLOAD',
      url,
      extraData,
      filePath,
      () async {
        try {
          final formData = dio.FormData();
          
          // 添加文件
          formData.files.add(MapEntry(
            'file',
            await dio.MultipartFile.fromFile(filePath, filename: fileName),
          ));
          
          // 添加额外数据
          if (extraData != null) {
            extraData.forEach((key, value) {
              formData.fields.add(MapEntry(key, value.toString()));
            });
          }

          final dioToken = cancelToken != null ? _getCancelToken(cancelToken) : null;

          final response = await _dio.post(
            url,
            data: formData,
            options: dio.Options(headers: headers),
            onSendProgress: onSendProgress,
            cancelToken: dioToken,
          );

          return _processResponse(response, fromJsonT, false);
        } on dio.DioException catch (e) {
          throw _handleDioException(e, url);
        } catch (e) {
          LogUtil.error('HttpManager upload file error,url:$url,detail: $e');
          rethrow;
        }
      },
      allowDuplicate: allowDuplicate,
    );
  }

  // 处理Token过期事件
  static void _handleTokenExpiration() {
    try {
      LogUtil.info('处理Token过期 (401错误) - 发送Token过期事件');
      
      // 发送Token过期事件通知
      EventBus().fire('token_expired', {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'reason': 'HTTP 401 response'
      });
      
      LogUtil.info('Token过期事件已发送，等待GlobalState处理');
    } catch (e) {
      LogUtil.error('处理Token过期时发生错误: $e');
    }
  }
}

class Response<T> {
  final int code;
  final String msg;
  final T? data;
  final dynamic rawData; // 添加原始数据字段
  final String errorCode;

  bool get isSuccess => code == 200 || code == 1;

  Response({required this.code, required this.msg, this.data, this.rawData, String? errorCode})
      : this.errorCode = errorCode ?? ErrorCodes.SERVER_ERROR;

  factory Response.fromJson(
      Map<String, dynamic> json, T Function(dynamic json)? fromJsonT) {
    // 获取响应码 - 可能是'code'
    int responseCode = 0;
    if (json.containsKey('code')) {
      responseCode = json['code'] as int;
    }
    
    // 获取消息 - 可能是'msg'或'message'
    String message = '';
    if (json.containsKey('msg')) {
      message = json['msg'] as String;
    } else if (json.containsKey('message')) {
      message = json['message'] as String;
    }
    
    // 获取数据 - 'data'字段
    dynamic responseData = json['data'];
    
    // 获取错误代码 - 可能是'error_code'字段
    String? errorCode;
    if (json.containsKey('error_code')) {
      errorCode = json['error_code'] as String;
    } else {
      // 根据响应码映射错误代码
      if (responseCode != 200 && responseCode != 1) {
        if (responseCode >= 500) {
          errorCode = ErrorCodes.SERVER_ERROR;
        } else if (responseCode == 401 || responseCode == 403) {
          errorCode = ErrorCodes.AUTH_ERROR;
        } else if (responseCode == 404) {
          errorCode = ErrorCodes.DATA_NOT_FOUND;
        } else {
          errorCode = ErrorCodes.GENERAL_ERROR;
        }
      }
    }
    
    if (fromJsonT == null) {
      return Response(
        code: responseCode,
        msg: message,
        rawData: json, // 保存原始数据
        errorCode: errorCode,
      );
    } else {
      try {
        final parsedData = responseData != null ? fromJsonT(responseData) : null;
        return Response(
          code: responseCode,
          msg: message,
          data: parsedData,
          rawData: json, // 保存原始数据
          errorCode: errorCode,
        );
      } catch (e) {
        return Response(
          code: responseCode,
          msg: message,
          data: null,
          rawData: json, // 保存原始数据
          errorCode: ErrorCodes.DATA_PARSE_ERROR,
        );
      }
    }
  }
  
  @override
  String toString() {
    return 'Response{code: $code, msg: $msg, data: $data, rawData: $rawData}';
  }
}