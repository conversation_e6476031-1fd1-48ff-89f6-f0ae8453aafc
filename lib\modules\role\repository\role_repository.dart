import 'package:rolio/common/interfaces/base_repository.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/constants/http_url_constants.dart';
import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/modules/role/model/search_filter.dart';
import 'package:rolio/env.dart';
import 'package:get/get.dart';

/// 角色仓库
///
/// 处理AI角色的业务逻辑，包括角色详情获取和收藏管理
class RoleRepository extends BaseRepository {
  // 缓存管理器
  final CacheManager _cacheManager = Get.find<CacheManager>();
  
  // 缓存键常量
  static const String _roleCacheKeyPrefix = 'role_';
  static const String _roleDetailCacheKeyPrefix = 'role_detail_';
  static const String _roleSearchCacheKeyPrefix = 'role_search_';
  
  // 缓存过期时间配置（毫秒）- 使用统一配置
  static const int _detailCacheExpiry = CacheConstants.roleDetailExpiryMs;  
  // 构造函数
  RoleRepository();
  
  // 获取API基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;
  
  // 确保URL格式正确
  String _ensureUrlFormat(String baseUrl, String path) {
    // 确保baseUrl不以斜杠结尾
    String normalizedBaseUrl = baseUrl;
    if (normalizedBaseUrl.endsWith('/')) {
      normalizedBaseUrl = normalizedBaseUrl.substring(0, normalizedBaseUrl.length - 1);
    }
    
    // 确保path以斜杠开头
    String normalizedPath = path;
    if (!normalizedPath.startsWith('/')) {
      normalizedPath = '/$normalizedPath';
    }
    
    final finalUrl = '$normalizedBaseUrl$normalizedPath';
    LogUtil.debug('URL构建: baseUrl=$baseUrl, path=$path, finalUrl=$finalUrl');
    return finalUrl;
  }

  /// 获取角色详情
  /// 
  /// 通过API获取指定角色的详细信息，包括收藏状态
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  /// 返回角色详情，如果不存在则返回null
  Future<AiRole?> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    final cacheKey = '${_roleDetailCacheKeyPrefix}${roleId}';
    
    try {
      // 如果不是强制刷新，尝试从缓存获取
      if (!forceRefresh) {
        final cachedRole = await _cacheManager.get<AiRole>(
          cacheKey,
          strategy: CacheStrategy.memoryThenPersistent,
          fromJson: (json) => AiRole.fromJson(json),
        );
        
        if (cachedRole != null) {
          LogUtil.debug('从缓存获取角色详情，ID: $roleId');
          return cachedRole;
        }
      }
      
      // 构建API URL - /roles/{role_id}
      final url = _ensureUrlFormat(_baseUrl, '${HttpUrl.rolePath}$roleId');
      LogUtil.debug('获取角色详情，URL: $url');
      
      // 发送请求
      final response = await HttpManager.get(url: url);
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取角色详情失败，响应为空');
        return null;
      }
      
      // 解析响应数据
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('角色详情响应数据结构异常，不是Map类型');
        return null;
      }
      
      if (!responseData.containsKey('data')) {
        LogUtil.warn('角色详情响应数据结构异常，data字段不存在');
        return null;
      }
      
      final dynamic dataField = responseData['data'];
      if (dataField is! Map<String, dynamic>) {
        LogUtil.warn('角色详情响应数据结构异常，data不是Map类型');
        return null;
      }
      
      // 创建角色对象
      try {
        final role = AiRole.fromJson(dataField);
        
        // 验证必要字段
        if (role.id <= 0 || role.name.isEmpty) {
          LogUtil.warn('角色详情数据验证失败: $dataField');
          return null;
        }
        
        // 缓存角色详情
        await _cacheManager.set(
          cacheKey,
          dataField,
          strategy: CacheStrategy.both,
          expiry: _detailCacheExpiry,
        );
        
        LogUtil.debug('成功获取角色详情: ${role.name} (ID: ${role.id})');
        return role;
      } catch (e) {
        LogUtil.error('解析角色详情数据失败: $e');
        return null;
      }
    } catch (e) {
      LogUtil.error('获取角色详情失败，ID: $roleId, 错误: $e');
      // 抛出异常而不是返回null，让调用者处理错误
      throw ErrorHandler.createAppException(e, 'load role detail failed');
    }
  }

  /// 收藏角色
  /// 
  /// 将指定角色添加到收藏夹
  /// [roleId] 角色ID
  /// 返回是否收藏成功和收藏时间（如果成功）
  Future<Map<String, dynamic>?> favoriteRole(int roleId) async {
    try {
      LogUtil.debug('收藏角色, ID: $roleId');
      
      // 构建请求URL，使用常量定义的路径
      final url = _ensureUrlFormat(_baseUrl, HttpUrl.favoritePath);
      
      // 发送POST请求
      final response = await HttpManager.post(
        url: url,
        body: {'role_id': roleId},
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('收藏角色失败，响应为空');
        return null;
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，不是Map类型');
        return null;
      }
      
      // 判断是否成功
      final success = responseData['code'] == 200;
      
      if (success && responseData['data'] != null) {
        LogUtil.debug('成功收藏角色');
        
        // 强制刷新缓存中的角色详情
        if (roleId > 0) {
          getRoleDetail(roleId, forceRefresh: true);
        }
        
        // 返回服务器响应的数据
        return {
          'success': true,
          'favorited_at': responseData['data']['favorited_at'],
        };
      } else {
        final message = responseData['message'] ?? '收藏失败';
        LogUtil.warn('收藏角色失败: $message');
        throw AppException(message.toString(), code: ErrorCodes.FAVORITE_FAILED);
      }
    } catch (e) {
      LogUtil.error('收藏角色异常: $e');
      throw ErrorHandler.createAppException(e, '收藏角色失败，请稍后再试');
    }
  }
  
  /// 取消收藏角色
  /// 
  /// 将指定角色从收藏夹中移除
  /// [roleId] 角色ID
  /// 返回是否取消成功
  Future<bool> unfavoriteRole(int roleId) async {
    try {
      LogUtil.debug('取消收藏角色, ID: $roleId');
      
      // 构建请求URL，使用常量定义的路径
      final url = _ensureUrlFormat(_baseUrl, '${HttpUrl.favoritePath}/$roleId');
      
      // 发送DELETE请求
      final response = await HttpManager.put(
        url: url,
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('取消收藏角色失败，响应为空');
        return false;
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，不是Map类型');
        return false;
      }
      
      // 判断是否成功
      final success = responseData['code'] == 200;
      
      if (success) {
        LogUtil.debug('成功取消收藏角色');
        
        // 强制刷新缓存中的角色详情
        if (roleId > 0) {
          getRoleDetail(roleId, forceRefresh: true);
        }
        
        return true;
      } else {
        final message = responseData['message'] ?? '取消收藏失败';
        LogUtil.warn('取消收藏角色失败: $message');
        throw AppException(message.toString(), code: ErrorCodes.UNFAVORITE_FAILED);
      }
    } catch (e) {
      LogUtil.error('取消收藏角色异常: $e');
      throw ErrorHandler.createAppException(e, '取消收藏角色失败，请稍后再试');
    }
  }

  /// 获取收藏的角色列表
  ///
  /// 返回用户收藏的所有角色
  /// [page] 页码，从0开始
  /// [size] 每页大小
  /// 返回收藏角色列表及分页信息
  Future<Map<String, dynamic>> getFavoritedRoles({int page = 1, int size = 10}) async {
    try {
      LogUtil.debug('获取收藏角色列表，page: $page, size: $size');
      
      // 构建请求URL，使用常量定义的路径
      final url = _ensureUrlFormat(_baseUrl, HttpUrl.favoritePath);
      
      // 添加分页参数
      final queryParameters = {
        'page': page.toString(),
        'size': size.toString(),
      };
      
      // 发送GET请求
      final response = await HttpManager.get(
        url: url,
        params: queryParameters,
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取收藏角色列表失败，响应为空');
        return {
          'items': <AiRole>[],
          'total': 0,
          'page': page,
          'size': size,
          'pages': 0
        };
      }
      
      // 解析响应
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，不是Map类型');
        return {
          'items': <AiRole>[],
          'total': 0,
          'page': page,
          'size': size,
          'pages': 0
        };
      }
      
      if (!responseData.containsKey('data')) {
        LogUtil.warn('响应数据结构异常，data字段不存在');
        return {
          'items': <AiRole>[],
          'total': 0,
          'page': page,
          'size': size,
          'pages': 0
        };
      }
      
      final dynamic dataField = responseData['data'];
      if (dataField is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，data不是Map类型');
        return {
          'items': <AiRole>[],
          'total': 0,
          'page': page,
          'size': size,
          'pages': 0
        };
      }
      
      // 解析角色列表
      List<AiRole> roles = [];
      
      if (dataField.containsKey('items') && dataField['items'] is List) {
        final List<dynamic> favoriteItems = dataField['items'];
        
        for (var item in favoriteItems) {
          try {
            if (item is Map<String, dynamic> && item.containsKey('role')) {
              final roleData = item['role'];
              if (roleData is Map<String, dynamic>) {
                final role = AiRole.fromJson(roleData);
                
                // 验证必要字段
                if (role.id <= 0 || role.name.isEmpty) {
                  LogUtil.warn('角色数据验证失败，跳过: $roleData');
                  continue;
                }
                
                roles.add(role);
              }
            }
          } catch (e) {
            LogUtil.error('解析角色数据失败: $e, 数据: $item');
          }
        }
      }
      
      // 提取分页信息
      final int total = dataField['total'] ?? 0;
      final int currentPage = dataField['page'] ?? page;
      final int pageSize = dataField['size'] ?? size;
      final int pages = dataField['pages'] ?? 0;
      
      LogUtil.debug('成功获取收藏角色列表: ${roles.length}个角色，总共: $total 个');
      
      return {
        'items': roles,
        'total': total,
        'page': currentPage,
        'size': pageSize,
        'pages': pages
      };
    } catch (e) {
      LogUtil.error('获取收藏角色列表失败: $e');
      throw ErrorHandler.createAppException(e, 'Failed to fetch favorite roles');
    }
  }
  
  
  // 清除缓存
  Future<void> clearCache() async {
    try {
      // 清除角色详情相关缓存
      final cacheKeys = await _cacheManager.getKeys();
      for (final key in cacheKeys) {
        if (key.startsWith(_roleCacheKeyPrefix) || 
            key.startsWith(_roleDetailCacheKeyPrefix) || 
            key.startsWith(_roleSearchCacheKeyPrefix)) {
          await _cacheManager.remove(key, strategy: CacheStrategy.both);
        }
      }
      
      LogUtil.debug('已清除角色缓存');
    } catch (e) {
      LogUtil.error('清除角色缓存失败: $e');
    }
  }
} 